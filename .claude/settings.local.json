{"permissions": {"allow": ["Bash(sqlite3:*)", "Bash(find:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(poetry run:*)", "Bash(PYTHONPATH=/Users/<USER>/Coding/datahero4/apps/backend/src poetry run python ../../test_cache_pipeline.py)", "<PERSON><PERSON>(poetry install:*)", "Bash(rm:*)", "<PERSON><PERSON>(poetry lock:*)", "Bash(ls:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(poetry:*)", "Bash(/usr/bin/python3:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(echo:*)", "Bash(railway status:*)", "Bash(brew install:*)", "<PERSON><PERSON>(railway login:*)", "<PERSON><PERSON>(railway whoami:*)", "<PERSON><PERSON>(chmod:*)", "Bash(RAILWAY_TOKEN=************************************ poetry run python ../../scripts/railway-api-migration.py)", "Bash(railway --version)", "Bash(brew:*)", "Bash(brew upgrade:*)", "Bash(railway logout:*)", "<PERSON><PERSON>(true)", "Bash(railway projects:*)", "<PERSON><PERSON>(railway help:*)", "Bash(railway list:*)", "Bash(railway link:*)", "Bash(railway service:*)", "Bash(railway variables:*)", "Bash(railway run psql:*)", "Bash(railway connect:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(git add:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "Bash(gh pr:*)", "Bash(npm run dev:frontend:*)", "Bash(npm run dev:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp)", "<PERSON><PERSON>(claude mcp:*)", "Bash(railway logs:*)", "Bash(railway deploy:*)", "Bash(railway up:*)", "mcp__playwright__create_note", "Bash(npm install:*)", "Bash(npm search:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:frontend-production-324f.up.railway.app)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(node:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(git clone:*)", "Bash(npm run build:*)", "Bash(npx:*)", "mcp__exa__web_search_exa", "Bash(git rm:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "Bash(railway run:*)", "Bash(railway shell:*)", "Bash(PYTHONPATH=/Users/<USER>/Coding/datahero4/apps/backend/src poetry run python ../../test_llm_provider_direct.py)", "Bash(./scripts/deploy-railway.sh:*)", "Bash(railway add:*)", "Bash(railway domain:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__github__search_repositories", "mcp__postgres__query", "Bash(npm run test:backend:*)", "WebFetch(domain:docs.railway.app)", "WebFetch(domain:docs.railway.com)", "Bash(git update-index:*)", "Bash(render --version)", "Bash(render login)", "<PERSON><PERSON>(render help)", "Bash(render services:*)", "<PERSON><PERSON>(render --help)", "Bash(render projects:*)", "WebFetch(domain:status.render.com)", "Bash(render logs:*)", "Bash(render deploy:*)", "Bash(render workspace set)", "Bash(render workspace set:*)", "Bash(render:*)", "Bash(npm start)", "Bash(npm run:*)", "<PERSON><PERSON>(time curl:*)", "WebFetch(domain:railway.app)", "WebFetch(domain:railway.com)", "Bash(docker build:*)", "Bash(railway redeploy:*)", "Bash(./dev.sh:*)", "Bash(bash:*)", "Bash(pgrep:*)", "Bash(railway:*)", "mcp__github__get_file_contents", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_snapshot", "Bash(git fetch:*)", "Bash(npm test:*)"], "deny": []}}