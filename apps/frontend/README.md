# DataHero4 Frontend

Dashboard React para Sistema Multi-Agentes de Análise de Dados Multissetorial

## ⚡ **NOVO: Dashboard Ultra-Rápido com Sistema de Snapshot**

🎯 **Performance Revolucionária**: Dashboard agora carrega em **19ms** (era 5-60s)

- ✅ **99.9% de melhoria** na velocidade de carregamento
- ✅ **Estratégia snapshot-first** com fallback automático
- ✅ **6 KPIs críticos** exibidos instantaneamente
- ✅ **Dados reais** do cliente L2M integrados
- ✅ **Interface responsiva** e moderna

## 📋 Visão Geral

O frontend do DataHero4 é uma aplicação React moderna que fornece uma interface intuitiva para interagir com o sistema de análise de dados baseado em LangGraph. Oferece funcionalidades de chat inteligente, visualização ultra-rápida de KPIs e feedback interativo.

## 🛠️ Tecnologias

- **React 18** - Framework principal
- **TypeScript** - Tipagem estática
- **Vite** - Build tool e dev server
- **shadcn/ui** - Componentes UI modernos
- **TailwindCSS** - Estilização utilitária
- **Motion.dev** - Animações performáticas
- **React Query** - Gerenciamento de estado servidor
- **React Router** - Roteamento
- **React Hook Form** - Formulários
- **Recharts** - Gráficos e visualizações

## 🚀 Desenvolvimento Local

### Pré-requisitos
- Node.js >= 18
- npm >= 9

### Instalação e Execução

```bash
# Do diretório raiz do monorepo
npm install

# Iniciar apenas o frontend
npm run dev:frontend

# Ou iniciar frontend + backend juntos
./scripts/dev.sh
```

### URLs de Desenvolvimento
- **Frontend**: http://localhost:3000
- **Dashboard**: http://localhost:3000/dashboard
- **Backend API**: http://localhost:8000
- **Dashboard API**: http://localhost:8000/api/dashboard
- **API Docs**: http://localhost:8000/docs

## 📁 Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Componentes base (shadcn/ui)
│   ├── chat/           # Componentes de chat
│   ├── dashboard/      # Componentes de dashboard e KPIs
│   ├── content/        # Componentes de conteúdo
│   ├── layout/         # Componentes de layout
│   └── login/          # Componentes de login
├── hooks/              # Custom hooks
├── lib/                # Utilitários e configurações
├── pages/              # Páginas da aplicação
├── types/              # Definições de tipos TypeScript
└── utils/              # Funções utilitárias
```

## 🎯 Funcionalidades Principais

### 🚀 Dashboard Ultra-Rápido (NOVO)
- **Performance**: Carregamento em 19ms com sistema de snapshot
- **KPIs Críticos**: 6 indicadores essenciais para operadoras de câmbio
- **Dados Reais**: Integração com banco L2M PostgreSQL
- **Estratégia Inteligente**: Snapshot-first com fallback automático
- **Responsivo**: Interface adaptável para desktop e mobile

### 💬 Interface de Chat Inteligente
- Chat em tempo real com o sistema DataHero
- Suporte a perguntas em linguagem natural
- Histórico de conversas persistente
- Feedback interativo com reprocessamento automático

### 📊 Dashboard de KPIs Integrado
- **34 KPIs em tempo real** across 7 business categories
- **Natural Language Editing** - modify dashboards using plain English
- **Interactive Charts** - line, bar, area, and gauge visualizations
- **Smart Alerts** - context-aware notifications with severity levels
- **Real-time Data** - live SQL execution with period filters
- **Performance Optimized** - lazy loading and caching for fast response

**Componentes Implementados:**
- `KpiBentoGrid.tsx` - Grid responsivo com layout Bento
- `KpiBentoCard.tsx` - Cards animados com gráficos (Recharts)
- `DashboardControls.tsx` - Controles de refresh e export
- `AddKpiModal.tsx` - Modal para adicionar KPIs ao dashboard
- `LoadingStates.tsx` - Estados de carregamento centralizados

**Interface Visual Completa:**
- ✅ **Animações Avançadas**: Motion.dev para transições suaves
- ✅ **Interatividade**: Drag & drop, priorização, edição modal
- ✅ **Responsividade**: Layout adaptativo para diferentes telas
- ✅ **Dados Reais**: Integração com backend via APIs de KPI
- ✅ **Backend Integration**: Conecta com endpoints `/api/kpis`

### 🔄 Sistema de Feedback Avançado
- Modal de feedback com categorias
- Opção de reprocessamento automático
- Análise semântica de correções
- Interface seamless para melhorias

### 🎨 Experiência do Usuário
- Design responsivo e moderno
- Animações suaves com Motion.dev
- Tema claro/escuro
- Componentes acessíveis

## Motion.dev Integration

Este projeto inclui uma integração completa com [Motion.dev](https://motion.dev/), uma biblioteca de animações moderna e performática para React, especialmente otimizada para dashboards financeiros e interfaces de chat.

### 🎯 Animações Implementadas

#### 1. KPI Cards (`KpiCard.tsx`)
- **Entrada suave**: Fade in + slide up ao carregar
- **Hover effects**: Scale sutil (1.02x) com shadow
- **Contador animado**: Valores numéricos animam ao mudar
- **Alertas dinâmicos**: Animação de altura com AnimatePresence
- **Badges de prioridade**: Entrada/saída suave
- **Botões de ação**: Aparecem com scale + fade no hover

#### 2. Chat Messages (`ChatMessage.tsx`)
- **Mensagens**: Entrada progressiva com stagger
- **Cards de conteúdo**: Hover scale + tap feedback
- **Transições de conteúdo**: AnimatePresence entre estados
- **Feedback buttons**: Animações de estado (positivo/negativo)

#### 3. Interface Principal (`Index.tsx`)
- **Título inicial**: Animação sequencial (fade + scale)
- **Lista de mensagens**: Stagger animation com delay progressivo
- **Loading states**: Spinner customizado com rotação suave
- **Transições de layout**: Smooth entre estados vazios/com conteúdo

#### 4. Input de Chat (`ChatInput.tsx`)
- **Focus effects**: Scale + border color no foco
- **Botão de envio**: Hover scale + loading spinner
- **Estados de loading**: Transição suave entre ícone e spinner

### 🛠️ Componentes e Hooks

#### Hooks Customizados (`useMotionAnimations.ts`)
```tsx
// Animações baseadas em scroll
const { ref, isInView } = useScrollAnimation();

// Efeitos parallax
const parallaxY = useParallax(0.5);

// Animações spring suaves
const { value, setValue } = useSmoothSpring(0);

// Contador animado
const animatedValue = useCounter(targetValue, duration);
```

#### Variantes Pré-definidas
- `fadeIn`, `fadeInUp`, `fadeInDown`
- `slideInLeft`, `slideInRight`
- `scaleIn`, `buttonHover`, `cardHover`
- `staggerContainer`, `staggerItem`

#### Presets de Transição
- `smooth`: Transições suaves padrão
- `bouncy`: Efeitos com spring
- `fast`: Animações rápidas
- `slow`: Transições lentas

### 📱 Casos de Uso Específicos

#### Dashboard Financeiro
```tsx
// KPI Card com animações
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  whileHover={{ scale: 1.02 }}
>
  <KpiCard data={kpiData} />
</motion.div>
```

#### Interface de Chat
```tsx
// Mensagens com stagger
<AnimatePresence>
  {messages.map((msg, index) => (
    <motion.div
      key={msg.id}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
    >
      <ChatMessage {...msg} />
    </motion.div>
  ))}
</AnimatePresence>
```

#### Estados de Loading
```tsx
// Spinner customizado
<motion.div
  className="spinner"
  animate={{ rotate: 360 }}
  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
/>
```

### 🎨 Página de Demonstração

Acesse a página `AnimationsDemo.tsx` para ver:
- KPI Cards interativos com todas as animações
- Sistema de chat com transições suaves
- Animações baseadas em scroll
- Diferentes tipos de loading states
- Exemplos práticos de implementação

### 🚀 Performance

- **Otimizado para 60fps**: Todas as animações usam propriedades GPU-accelerated
- **Lazy loading**: Animações só executam quando necessário
- **Cancelamento automático**: Cleanup de animações em componentes desmontados
- **Reduced motion**: Respeita preferências de acessibilidade do usuário

## 🧪 Testes

```bash
# Executar testes
npm test

# Executar testes em modo watch
npm run test:watch

# Executar testes com coverage
npm run test:coverage
```

## 🏗️ Build e Deploy

### Build Local
```bash
# Build para produção
npm run build

# Preview do build
npm run preview
```

### Deploy
O frontend é automaticamente deployado como parte do monorepo. Veja o arquivo `railway.toml` na raiz do projeto para configurações específicas.

### Variáveis de Ambiente
```bash
# .env.local
VITE_API_BASE_URL=https://your-backend-domain.com
```

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -m 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📚 Documentação Adicional

- [Documentação da API](../../docs/API.md)
- [Guia de Deployment](../../docs/DEPLOYMENT.md)
- [Arquitetura do Sistema](../backend/docs/guides/ARCHITECTURE.md)
- [Changelog](../backend/docs/development/CHANGELOG.md)
