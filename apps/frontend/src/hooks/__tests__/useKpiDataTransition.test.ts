/**
 * Tests for useKpiDataTransition Hook
 * 
 * Tests the compatibility layer between old and new architecture
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDashboardFilters, useKpiData } from '../useKpiDataTransition';

// Mock the dynamic hooks
vi.mock('../useDynamicFilters', () => ({
  useDynamicFilters: vi.fn(() => ({
    filters: { timeframe: 'week', currency: 'all' },
    updateFilter: vi.fn(),
    isLoading: false
  }))
}));

vi.mock('../useDynamicKpis', () => ({
  useDynamicKpis: vi.fn(() => ({
    kpis: [],
    isInitialLoading: false,
    isFilterChanging: false,
    isRefreshing: false,
    isLoading: false,
    error: null,
    hasError: false,
    refreshKpis: vi.fn(),
    togglePriority: vi.fn(),
    removeKpi: vi.fn(),
    totalCount: 0,
    lastUpdated: null
  }))
}));

describe('useKpiDataTransition', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useDashboardFilters', () => {
    it('should provide legacy-compatible filter interface', () => {
      const { result } = renderHook(() => useDashboardFilters());

      expect(result.current.filters).toEqual({
        timeframe: 'week',
        currency: 'all'
      });
      expect(typeof result.current.updateTimeframe).toBe('function');
      expect(typeof result.current.updateCurrency).toBe('function');
    });

    it('should update timeframe correctly', () => {
      const { result } = renderHook(() => useDashboardFilters());

      act(() => {
        result.current.updateTimeframe('month');
      });

      // The actual update is mocked, but we can verify the function was called
      expect(result.current.updateTimeframe).toBeDefined();
    });

    it('should update currency correctly', () => {
      const { result } = renderHook(() => useDashboardFilters());

      act(() => {
        result.current.updateCurrency('usd');
      });

      // The actual update is mocked, but we can verify the function was called
      expect(result.current.updateCurrency).toBeDefined();
    });
  });

  describe('useKpiData', () => {
    it('should provide legacy-compatible KPI data interface', () => {
      const legacyFilters = { timeframe: 'week' as const, currency: 'all' as const };
      const { result } = renderHook(() => useKpiData(legacyFilters));

      expect(result.current.kpis).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isInitialLoading).toBe(false);
      expect(result.current.isFilterChanging).toBe(false);
      expect(result.current.isRefreshing).toBe(false);
      expect(result.current.error).toBe(null);
      expect(typeof result.current.togglePriority).toBe('function');
      expect(typeof result.current.refreshKpis).toBe('function');
      expect(typeof result.current.removeKpi).toBe('function');
    });

    it('should convert legacy filters to dynamic filters', () => {
      const legacyFilters = { timeframe: 'month' as const, currency: 'usd' as const };
      const { result } = renderHook(() => useKpiData(legacyFilters));

      // Should provide dynamic filters for components that need them
      expect(result.current.dynamicFilters).toBeDefined();
      expect(result.current.dynamicFilters.timeframe).toBe('month');
      expect(result.current.dynamicFilters.currency).toBe('usd');
    });

    it('should include default dynamic filter values', () => {
      const legacyFilters = { timeframe: 'week' as const, currency: 'all' as const };
      const { result } = renderHook(() => useKpiData(legacyFilters));

      const dynamicFilters = result.current.dynamicFilters;
      expect(dynamicFilters.sector).toBe('cambio');
      expect(dynamicFilters.client_id).toBe('L2M');
      expect(dynamicFilters.priority_only).toBe(false);
    });
  });
});
