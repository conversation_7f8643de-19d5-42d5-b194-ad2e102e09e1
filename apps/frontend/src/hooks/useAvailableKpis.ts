import { useState, useEffect } from 'react';
import { getAvailableKpis, type AvailableKpi } from '@/lib/api';

export const useAvailableKpis = (sector: string = 'cambio') => {
  const [availableKpis, setAvailableKpis] = useState<AvailableKpi[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAvailableKpis = async () => {
      console.log('🔄 [useAvailableKpis] Loading available KPIs...');
      setIsLoading(true);
      setError(null);

      try {
        const kpis = await getAvailableKpis(sector);
        console.log('✅ [useAvailableKpis] Loaded KPIs:', kpis.length);
        setAvailableKpis(kpis);
      } catch (err) {
        console.error('❌ [useAvailableKpis] Error loading KPIs:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load available KPIs';
        setError(errorMessage);

        // Fail fast - não usar dados mock
        setAvailableKpis([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadAvailableKpis();
  }, [client_id, sector]);

  const refresh = async () => {
    await loadAvailableKpis();
  };

  return {
    availableKpis,
    isLoading,
    error,
    refresh
  };
};