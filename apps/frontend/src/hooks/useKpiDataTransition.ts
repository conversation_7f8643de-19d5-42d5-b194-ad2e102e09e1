/**
 * KPI Data Transition Hook
 * 
 * Temporary hook that maintains compatibility with existing Dashboard.tsx
 * while internally using the new dynamic architecture. This allows us to
 * refactor without breaking the current interface.
 */

import { useMemo } from 'react';
import { useDynamicKpis } from './useDynamicKpis';
import { useDynamicFilters } from './useDynamicFilters';
import { DashboardFilters } from '@/types/dashboard';

// Legacy filter type for compatibility
export type CurrencyOption = 'all' | 'usd' | 'eur' | 'gbp';
export type TimeframeOption = '1d' | 'week' | 'month' | 'quarter';

interface LegacyFilters {
  timeframe: TimeframeOption;
  currency: CurrencyOption;
}

interface UseDashboardFiltersReturn {
  filters: LegacyFilters;
  updateTimeframe: (timeframe: TimeframeOption) => void;
  updateCurrency: (currency: CurrencyOption) => void;
}

/**
 * Legacy-compatible filters hook
 */
export const useDashboardFilters = (): UseDashboardFiltersReturn => {
  const { filters, updateFilter } = useDynamicFilters({
    initialFilters: {
      timeframe: 'week',
      currency: 'all'
    }
  });

  const legacyFilters: LegacyFilters = {
    timeframe: (filters.timeframe as TimeframeOption) || 'week',
    currency: (filters.currency as CurrencyOption) || 'all'
  };

  const updateTimeframe = (timeframe: TimeframeOption) => {
    updateFilter('timeframe', timeframe);
  };

  const updateCurrency = (currency: CurrencyOption) => {
    updateFilter('currency', currency);
  };

  return {
    filters: legacyFilters,
    updateTimeframe,
    updateCurrency
  };
};

/**
 * Legacy-compatible KPI data hook
 */
export const useKpiData = (legacyFilters: LegacyFilters) => {
  // Convert legacy filters to dynamic filters
  const dynamicFilters: DashboardFilters = useMemo(() => ({
    timeframe: legacyFilters.timeframe,
    currency: legacyFilters.currency,
    // Add other default filters as needed
    sector: 'cambio',
    client_id: 'L2M',
    priority_only: false
  }), [legacyFilters]);

  const {
    kpis,
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    isLoading,
    error,
    hasError,
    refreshKpis,
    togglePriority,
    removeKpi,
    totalCount,
    lastUpdated
  } = useDynamicKpis({
    filters: dynamicFilters,
    autoRefresh: false
  });

  return {
    kpis,
    isLoading,
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    error,
    hasError,
    togglePriority,
    refreshKpis,
    removeKpi,
    totalCount,
    lastUpdated,
    // Provide dynamic filters for components that need them
    dynamicFilters
  };
};

// Re-export types for compatibility
export type { KpiData } from '@/types/dashboard';
