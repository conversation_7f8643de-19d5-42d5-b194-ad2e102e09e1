/**
 * Tests for KpiBentoGrid Component
 * 
 * Tests the responsive grid layout and KPI rendering
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import KpiBentoGrid from '../KpiBentoGrid';
import { KpiData, DashboardFilters } from '@/types/dashboard';

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  }
}));

// Mock KpiBentoCard component
vi.mock('../KpiBentoCard', () => ({
  default: ({ kpi, isLarge }: { kpi: KpiData; isLarge: boolean }) => (
    <div data-testid={`kpi-card-${kpi.id}`} data-large={isLarge}>
      <h3>{kpi.name}</h3>
      <span>{kpi.value}</span>
    </div>
  )
}));

const mockFilters: DashboardFilters = {
  timeframe: 'week',
  currency: 'all',
  sector: 'cambio',
  client_id: 'L2M',
  priority_only: false
};

const mockKpis: KpiData[] = [
  {
    id: 'volume_total',
    name: 'Volume Total',
    value: 1000000,
    formattedValue: '$1,000,000',
    change: 5.2,
    trend: 'up',
    category: 'volume',
    priority: true,
    unit: 'currency',
    description: 'Total trading volume'
  },
  {
    id: 'ticket_medio',
    name: 'Ticket Médio',
    value: 5000,
    formattedValue: '$5,000',
    change: -2.1,
    trend: 'down',
    category: 'performance',
    priority: true,
    unit: 'currency',
    description: 'Average ticket size'
  }
];

describe('KpiBentoGrid', () => {
  const defaultProps = {
    kpis: mockKpis,
    filters: mockFilters,
    onTogglePriority: vi.fn(),
    onRemoveKpi: vi.fn(),
    periodData: {
      currentDate: new Date().toISOString(),
      isSimulated: false
    }
  };

  it('should render all KPIs', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    expect(screen.getByTestId('kpi-card-volume_total')).toBeInTheDocument();
    expect(screen.getByTestId('kpi-card-ticket_medio')).toBeInTheDocument();
  });

  it('should apply large size to first 2 KPIs when there are 5+ KPIs', () => {
    const manyKpis = [
      ...mockKpis,
      { ...mockKpis[0], id: 'kpi3', name: 'KPI 3' },
      { ...mockKpis[0], id: 'kpi4', name: 'KPI 4' },
      { ...mockKpis[0], id: 'kpi5', name: 'KPI 5' }
    ];

    render(<KpiBentoGrid {...defaultProps} kpis={manyKpis} />);

    expect(screen.getByTestId('kpi-card-volume_total')).toHaveAttribute('data-large', 'true');
    expect(screen.getByTestId('kpi-card-ticket_medio')).toHaveAttribute('data-large', 'true');
    expect(screen.getByTestId('kpi-card-kpi3')).toHaveAttribute('data-large', 'false');
  });

  it('should not apply large size when there are fewer than 5 KPIs', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    expect(screen.getByTestId('kpi-card-volume_total')).toHaveAttribute('data-large', 'false');
    expect(screen.getByTestId('kpi-card-ticket_medio')).toHaveAttribute('data-large', 'false');
  });

  it('should handle empty KPI list', () => {
    render(<KpiBentoGrid {...defaultProps} kpis={[]} />);

    expect(screen.queryByTestId(/kpi-card-/)).not.toBeInTheDocument();
  });

  it('should render with correct grid structure', () => {
    const { container } = render(<KpiBentoGrid {...defaultProps} />);

    const gridContainer = container.querySelector('.grid.grid-cols-12');
    expect(gridContainer).toBeInTheDocument();
  });
});
