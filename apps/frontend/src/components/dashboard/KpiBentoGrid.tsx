import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { KpiData, DashboardFilters } from '@/types/dashboard';
import KpiBentoCard from './KpiBentoCard';

interface KpiBentoGridProps {
  kpis: KpiData[];
  filters: DashboardFilters;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  periodData?: {
    currentDate: string;
    isSimulated: boolean;
    periodDescription?: string | null;
  };
}

const KpiBentoGrid: React.FC<KpiBentoGridProps> = ({
  kpis,
  filters,
  onTogglePriority,
  onRemoveKpi,
  periodData
}) => {
  // Dynamic grid layout that adapts to number of KPIs
  const getGridClassName = (index: number, totalKpis: number) => {
    // Priority KPIs (first 2) get larger cards
    const isPriority = index < 2;

    if (totalKpis <= 2) {
      // 1-2 KPIs: Full width cards
      return "col-span-12 row-span-2";
    } else if (totalKpis <= 4) {
      // 3-4 KPIs: 2x2 grid
      return "col-span-12 md:col-span-6 row-span-2";
    } else if (totalKpis <= 6) {
      // 5-6 KPIs: Bento layout (2 large + 4 medium)
      if (isPriority) {
        return "col-span-12 md:col-span-6 row-span-2";
      } else {
        return "col-span-6 md:col-span-3 row-span-1";
      }
    } else {
      // 7+ KPIs: Uniform grid
      return "col-span-6 md:col-span-4 lg:col-span-3 row-span-1";
    }
  };

  return (
    <div className="w-full px-4 md:px-6 lg:px-8">
      <motion.div 
        className="grid grid-cols-12 gap-4 md:gap-6 auto-rows-[minmax(180px,_1fr)]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {kpis.map((kpi, index) => (
          <motion.div
            key={kpi.id}
            className={cn(
              getGridClassName(index, kpis.length),
              "min-h-0" // Allow cards to size based on content
            )}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ 
              duration: 0.5, 
              delay: index * 0.1,
              ease: "easeOut"
            }}
          >
            <KpiBentoCard
              kpi={kpi}
              filters={filters}
              onTogglePriority={onTogglePriority}
              onRemoveKpi={onRemoveKpi}
              isLarge={index < 2 && kpis.length >= 5}
              periodData={periodData}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default KpiBentoGrid;