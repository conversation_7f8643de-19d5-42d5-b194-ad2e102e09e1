/**
 * Dashboard Types - Centralized type definitions
 * 
 * This file contains all TypeScript interfaces and types used across
 * the dashboard system, providing a single source of truth for data contracts.
 */

// ============================================================================
// CORE DATA TYPES
// ============================================================================

export interface KpiData {
  id: string;
  title: string;
  description: string;
  currentValue: number;
  previousValue?: number;
  changePercent?: number;
  trend: 'up' | 'down' | 'stable';
  format: 'currency' | 'percentage' | 'number';
  unit?: string;
  chartType: 'line' | 'area' | 'bar';
  chartData: ChartDataPoint[];
  isPriority: boolean;
  category: string;
  order: number;
  alert?: KpiAlert;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
}

export interface KpiAlert {
  id: string;
  type: 'above' | 'below';
  threshold: number;
  severity: 'low' | 'medium' | 'high';
  message: string;
  isActive: boolean;
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export type TimeframeOption = '1d' | 'week' | 'month' | 'quarter';
export type CurrencyOption = 'all' | 'usd' | 'eur' | 'gbp';

export interface DashboardFilters {
  timeframe: TimeframeOption;
  currency: CurrencyOption;
}

// ============================================================================
// UI STATE TYPES
// ============================================================================

export interface LoadingState {
  isInitialLoading: boolean;
  isFilterChanging: boolean;
  isRefreshing: boolean;
  isLoading: boolean; // Computed: any of the above
}

export interface ErrorState {
  error: string | null;
  hasError: boolean; // Computed: error !== null
}

export interface DashboardState extends LoadingState, ErrorState {
  kpis: KpiData[];
  filters: DashboardFilters;
  snapshotMetadata: any;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface PeriodData {
  currentDate: string;
  isSimulated: boolean;
  periodDescription?: string | null;
}

export interface KpiCardProps {
  kpi: KpiData;
  currency: CurrencyOption;
  isLarge?: boolean;
  periodData?: PeriodData;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
}

export interface KpiGridProps {
  kpis: KpiData[];
  currency: CurrencyOption;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  periodData?: PeriodData;
}

export interface DashboardControlsProps {
  filters: DashboardFilters;
  onTimeframeChange: (timeframe: TimeframeOption) => void;
  onCurrencyChange: (currency: CurrencyOption) => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onAddKpi?: () => void;
  isRefreshing?: boolean;
}

// ============================================================================
// ACTION TYPES
// ============================================================================

export interface KpiActions {
  togglePriority: (kpiId: string) => void;
  removeKpi: (kpiId: string) => void;
  refreshKpis: () => Promise<void>;
}

export interface FilterActions {
  updateTimeframe: (timeframe: TimeframeOption) => void;
  updateCurrency: (currency: CurrencyOption) => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UseKpiDataReturn extends LoadingState, ErrorState {
  kpis: KpiData[];
  snapshotMetadata: any;
  togglePriority: (kpiId: string) => void;
  refreshKpis: () => Promise<void>;
}

export interface UseDashboardFiltersReturn {
  filters: DashboardFilters;
  updateTimeframe: (timeframe: TimeframeOption) => void;
  updateCurrency: (currency: CurrencyOption) => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
}

// ============================================================================
// LAYOUT TYPES
// ============================================================================

export type GridLayoutType = 'bento' | 'uniform' | 'masonry';

export interface GridLayoutConfig {
  type: GridLayoutType;
  columns: number;
  gap: number;
  minCardHeight: number;
  maxCardHeight?: number;
}

export interface CardSizeConfig {
  isLarge: boolean;
  span: number;
  height: string;
}

// ============================================================================
// FORMATTING TYPES
// ============================================================================

export interface FormatConfig {
  locale: string;
  currency: string;
  minimumFractionDigits: number;
  maximumFractionDigits: number;
}

export interface FormattingOptions {
  format: KpiData['format'];
  currency: CurrencyOption;
  value: number;
}

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface KpiSelectEvent {
  kpiId: string;
  element: HTMLElement;
  cardRect: DOMRect;
}

export interface KpiEventHandlers {
  onKpiSelect: (event: KpiSelectEvent) => void;
  onKpiHover: (kpiId: string, isHovered: boolean) => void;
  onKpiAction: (kpiId: string, action: string) => void;
}

// ============================================================================
// API TYPES
// ============================================================================

export interface DashboardSnapshot {
  data: any;
  metadata: {
    date: string;
    period: {
      current_date: string;
      is_simulated: boolean;
      period_description?: string;
    };
    generated_at: string;
  };
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
  timestamp: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Re-export commonly used types for convenience
export type { KpiData as Kpi };
export type { DashboardFilters as Filters };
export type { LoadingState as Loading };
export type { ErrorState as Error };
