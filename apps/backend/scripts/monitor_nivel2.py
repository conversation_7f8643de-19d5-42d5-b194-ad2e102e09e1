#!/usr/bin/env python3
"""
Script para monitoramento de performance do Nível 2.
<PERSON><PERSON> métric<PERSON>, gera relatórios e monitora uso dos componentes.
"""
import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import typer

app = typer.Typer(
    name="monitor_nivel2",
    help="Monitoramento de performance do DataHero Nível 2",
    add_completion=False
)

@dataclass
class Nivel2Metrics:
    """Métricas do Nível 2."""
    sector: str
    client: str
    enabled: bool
    pattern_matches: int = 0
    total_queries: int = 0
    pattern_match_rate: float = 0.0
    token_reduction_avg: float = 0.0
    rag_examples: int = 0
    column_corrections: int = 0
    extractors_count: int = 0
    patterns_count: int = 0
    last_activity: Optional[str] = None
    components_status: Dict[str, bool] = None
    
    def __post_init__(self):
        if self.components_status is None:
            self.components_status = {}
        
        # Calcular pattern match rate
        if self.total_queries > 0:
            self.pattern_match_rate = self.pattern_matches / self.total_queries
        else:
            self.pattern_match_rate = 0.0

def collect_file_metrics(base_path: Path) -> Dict[str, Any]:
    """Coleta métricas dos arquivos de configuração."""
    metrics = {
        'extractors_count': 0,
        'patterns_count': 0,
        'aliases_count': 0,
        'config_size': 0,
        'last_modified': None
    }
    
    try:
        # Domain config metrics
        domain_config_path = base_path / "domain_config.json"
        if domain_config_path.exists():
            with open(domain_config_path, 'r') as f:
                domain_config = json.load(f)
            
            extractors = domain_config.get('extractors', {})
            metrics['extractors_count'] = len(extractors)
            metrics['config_size'] += domain_config_path.stat().st_size
            
            mod_time = datetime.fromtimestamp(domain_config_path.stat().st_mtime)
            if metrics['last_modified'] is None or mod_time > metrics['last_modified']:
                metrics['last_modified'] = mod_time
        
        # Patterns metrics
        patterns_path = base_path / "patterns.json"
        if patterns_path.exists():
            with open(patterns_path, 'r') as f:
                patterns_config = json.load(f)
            
            # Contar patterns válidos (não comentários)
            valid_patterns = [k for k in patterns_config.keys() if not k.startswith('//')]
            metrics['patterns_count'] = len(valid_patterns)
            metrics['config_size'] += patterns_path.stat().st_size
            
            mod_time = datetime.fromtimestamp(patterns_path.stat().st_mtime)
            if metrics['last_modified'] is None or mod_time > metrics['last_modified']:
                metrics['last_modified'] = mod_time
        
        # Column aliases metrics
        aliases_path = base_path / "column_aliases.json"
        if aliases_path.exists():
            with open(aliases_path, 'r') as f:
                aliases_config = json.load(f)
            
            aliases_gerais = aliases_config.get('aliases_gerais', {})
            column_aliases = aliases_config.get('column_aliases', {})
            sql_patterns = aliases_config.get('sql_patterns', [])
            
            metrics['aliases_count'] = len(aliases_gerais) + len(column_aliases) + len(sql_patterns)
            metrics['config_size'] += aliases_path.stat().st_size
            
            mod_time = datetime.fromtimestamp(aliases_path.stat().st_mtime)
            if metrics['last_modified'] is None or mod_time > metrics['last_modified']:
                metrics['last_modified'] = mod_time
        
    except Exception as e:
        print(f"Erro ao coletar métricas de arquivos: {e}")
    
    return metrics

def collect_rag_metrics(sector: str, client: str) -> Dict[str, Any]:
    """Coleta métricas do sistema RAG."""
    metrics = {
        'examples_count': 0,
        'total_size': 0,
        'last_example': None
    }
    
    try:
        rag_dir = Path("src/config/setores/rag_examples") / f"{sector}_{client}"
        if rag_dir.exists():
            example_files = list(rag_dir.glob("*.json"))
            metrics['examples_count'] = len(example_files)
            
            for file_path in example_files:
                metrics['total_size'] += file_path.stat().st_size
                
                mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if metrics['last_example'] is None or mod_time > metrics['last_example']:
                    metrics['last_example'] = mod_time
    
    except Exception as e:
        print(f"Erro ao coletar métricas RAG: {e}")
    
    return metrics

def test_components_status(sector: str, client: str) -> Dict[str, bool]:
    """Testa status dos componentes do Nível 2."""
    status = {
        'domain_extractor': False,
        'pattern_matcher': False,
        'column_matcher': False,
        'schema_compactor': False,
        'rag': False
    }
    
    try:
        from src.tools.nivel2_factory import Nivel2Factory
        
        # Test domain extractor
        try:
            extractor = Nivel2Factory.create_domain_extractor(sector, client)
            status['domain_extractor'] = extractor is not None
        except Exception:
            status['domain_extractor'] = False
        
        # Test pattern matcher
        try:
            matcher = Nivel2Factory.create_pattern_matcher(sector, client)
            status['pattern_matcher'] = matcher is not None
        except Exception:
            status['pattern_matcher'] = False
        
        # Test column matcher
        try:
            # Usar schema exemplo para teste
            schema_path = "src/config/setores/cambio/L2M/L2M_schema.json"
            col_matcher = Nivel2Factory.create_column_matcher(sector, client, schema_path)
            status['column_matcher'] = col_matcher is not None
        except Exception:
            status['column_matcher'] = False
        
        # Test schema compactor
        try:
            compactor = Nivel2Factory.create_schema_compactor(sector, client)
            status['schema_compactor'] = compactor is not None
        except Exception:
            status['schema_compactor'] = False
        
        # Test RAG
        try:
            rag = Nivel2Factory.create_rag(sector, client)
            status['rag'] = rag is not None
        except Exception:
            status['rag'] = False
    
    except ImportError:
        print("Aviso: Não foi possível importar Nivel2Factory para teste de componentes")
    
    return status

def collect_usage_metrics(sector: str, client: str) -> Dict[str, Any]:
    """Coleta métricas de uso a partir de logs (simulado)."""
    # Esta função simula coleta de métricas de logs
    # Em implementação real, analisaria logs de aplicação
    
    metrics = {
        'total_queries': 0,
        'pattern_matches': 0,
        'column_corrections': 0,
        'token_reduction_samples': [],
        'recent_activity': []
    }
    
    # Get real metrics from database or logs
    # No simulation - require real data sources

    # Check if configuration exists and get real metrics
    base_path = Path("src/config/setores") / sector / client / "nivel2"
    if base_path.exists():
        # Get real metrics from database or log files
        # This would connect to actual monitoring systems
        logger.warning("⚠️ Real metrics collection not implemented - returning empty metrics")
        # TODO: Implement real metrics collection from database/logs
    
    return metrics

def generate_metrics_for_client(sector: str, client: str) -> Nivel2Metrics:
    """Gera métricas completas para um cliente."""
    from src.tools.nivel2_factory import Nivel2Factory
    
    base_path = Path("src/config/setores") / sector / client / "nivel2"
    enabled = Nivel2Factory.is_nivel2_enabled(sector, client)
    
    metrics = Nivel2Metrics(
        sector=sector,
        client=client,
        enabled=enabled
    )
    
    if not enabled:
        return metrics
    
    # Coletar métricas de arquivos
    file_metrics = collect_file_metrics(base_path)
    metrics.extractors_count = file_metrics['extractors_count']
    metrics.patterns_count = file_metrics['patterns_count']
    
    if file_metrics['last_modified']:
        metrics.last_activity = file_metrics['last_modified'].isoformat()
    
    # Coletar métricas RAG
    rag_metrics = collect_rag_metrics(sector, client)
    metrics.rag_examples = rag_metrics['examples_count']
    
    # Coletar métricas de uso
    usage_metrics = collect_usage_metrics(sector, client)
    metrics.total_queries = usage_metrics['total_queries']
    metrics.pattern_matches = usage_metrics['pattern_matches']
    metrics.column_corrections = usage_metrics['column_corrections']
    
    # Testar componentes
    metrics.components_status = test_components_status(sector, client)
    
    # Calcular pattern match rate
    if metrics.total_queries > 0:
        metrics.pattern_match_rate = metrics.pattern_matches / metrics.total_queries
    
    return metrics

@app.command()
def status(
    sector: str = typer.Argument(..., help="Nome do setor"),
    client: str = typer.Argument(..., help="ID do cliente"),
    detailed: bool = typer.Option(False, "--detailed", "-d", help="Mostrar informações detalhadas")
):
    """
    Mostra status atual do Nível 2 para um cliente.
    
    Exemplo:
        python monitor_nivel2.py status cambio L2M
        python monitor_nivel2.py status vendas CLIENTE1 --detailed
    """
    typer.echo(f"📊 Status do Nível 2 - {sector}/{client}")
    
    metrics = generate_metrics_for_client(sector, client)
    
    # Status principal
    if metrics.enabled:
        typer.echo("✅ Nível 2 Habilitado")
    else:
        typer.echo("❌ Nível 2 Não Habilitado")
        return
    
    # Configuração
    typer.echo(f"\n📋 Configuração:")
    typer.echo(f"   Extractors: {metrics.extractors_count}")
    typer.echo(f"   Patterns: {metrics.patterns_count}")
    typer.echo(f"   Exemplos RAG: {metrics.rag_examples}")
    
    # Performance (se houver dados)
    if metrics.total_queries > 0:
        typer.echo(f"\n⚡ Performance:")
        typer.echo(f"   Total de consultas: {metrics.total_queries}")
        typer.echo(f"   Pattern matches: {metrics.pattern_matches}")
        typer.echo(f"   Taxa de match: {metrics.pattern_match_rate:.1%}")
        typer.echo(f"   Correções aplicadas: {metrics.column_corrections}")
    
    # Status dos componentes
    if detailed:
        typer.echo(f"\n🔧 Status dos Componentes:")
        for component, status in metrics.components_status.items():
            status_icon = "✅" if status else "❌"
            typer.echo(f"   {status_icon} {component}")
    
    # Atividade recente
    if metrics.last_activity:
        last_activity = datetime.fromisoformat(metrics.last_activity)
        time_diff = datetime.now() - last_activity
        if time_diff.days > 0:
            activity_str = f"{time_diff.days} dias atrás"
        elif time_diff.seconds > 3600:
            activity_str = f"{time_diff.seconds // 3600} horas atrás"
        else:
            activity_str = "recente"
        
        typer.echo(f"\n🕒 Última atividade: {activity_str}")

@app.command()
def report(
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Arquivo de saída (JSON)"),
    format: str = typer.Option("table", "--format", "-f", help="Formato: table, json, csv")
):
    """
    Gera relatório completo de todas as configurações Nível 2.
    
    Exemplo:
        python monitor_nivel2.py report
        python monitor_nivel2.py report --output report.json --format json
    """
    typer.echo("📊 Gerando relatório do Nível 2...")
    
    setores_path = Path("src/config/setores")
    if not setores_path.exists():
        typer.echo("❌ Diretório de setores não encontrado")
        raise typer.Exit(1)
    
    # Coletar métricas de todos os clientes
    all_metrics = []
    
    for setor_dir in setores_path.iterdir():
        if setor_dir.is_dir() and setor_dir.name != "nivel2_template":
            for client_dir in setor_dir.iterdir():
                if client_dir.is_dir():
                    nivel2_dir = client_dir / "nivel2"
                    if nivel2_dir.exists():
                        metrics = generate_metrics_for_client(setor_dir.name, client_dir.name)
                        all_metrics.append(metrics)
    
    if not all_metrics:
        typer.echo("❌ Nenhuma configuração Nível 2 encontrada")
        raise typer.Exit(1)
    
    # Estatísticas gerais
    total_clients = len(all_metrics)
    enabled_clients = sum(1 for m in all_metrics if m.enabled)
    total_extractors = sum(m.extractors_count for m in all_metrics)
    total_patterns = sum(m.patterns_count for m in all_metrics)
    total_rag_examples = sum(m.rag_examples for m in all_metrics)
    
    # Mostrar ou salvar relatório
    if format == "table":
        typer.echo(f"\n📈 Resumo Geral:")
        typer.echo(f"   Total de clientes: {total_clients}")
        typer.echo(f"   Clientes ativos: {enabled_clients}")
        typer.echo(f"   Total de extractors: {total_extractors}")
        typer.echo(f"   Total de patterns: {total_patterns}")
        typer.echo(f"   Total de exemplos RAG: {total_rag_examples}")
        
        typer.echo(f"\n📋 Detalhes por Cliente:")
        typer.echo("Setor/Cliente".ljust(20) + "Status".ljust(10) + "Extractors".ljust(12) + "Patterns".ljust(10) + "RAG")
        typer.echo("-" * 70)
        
        for metrics in all_metrics:
            client_name = f"{metrics.sector}/{metrics.client}"
            status = "✅" if metrics.enabled else "❌"
            
            line = (
                client_name.ljust(20) + 
                status.ljust(10) + 
                str(metrics.extractors_count).ljust(12) + 
                str(metrics.patterns_count).ljust(10) + 
                str(metrics.rag_examples)
            )
            typer.echo(line)
    
    elif format == "json":
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_clients': total_clients,
                'enabled_clients': enabled_clients,
                'total_extractors': total_extractors,
                'total_patterns': total_patterns,
                'total_rag_examples': total_rag_examples
            },
            'clients': [asdict(m) for m in all_metrics]
        }
        
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            typer.echo(f"✅ Relatório salvo em: {output}")
        else:
            typer.echo(json.dumps(report_data, indent=2, ensure_ascii=False, default=str))
    
    elif format == "csv":
        import csv
        import io
        
        output_stream = io.StringIO()
        
        writer = csv.writer(output_stream)
        writer.writerow([
            'Setor', 'Cliente', 'Habilitado', 'Extractors', 'Patterns', 
            'RAG_Examples', 'Total_Queries', 'Pattern_Matches', 'Match_Rate'
        ])
        
        for m in all_metrics:
            writer.writerow([
                m.sector, m.client, m.enabled, m.extractors_count, m.patterns_count,
                m.rag_examples, m.total_queries, m.pattern_matches, f"{m.pattern_match_rate:.2%}"
            ])
        
        csv_content = output_stream.getvalue()
        
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                f.write(csv_content)
            typer.echo(f"✅ Relatório CSV salvo em: {output}")
        else:
            typer.echo(csv_content)

@app.command()
def health_check():
    """
    Executa verificação de saúde do sistema Nível 2.
    Verifica configurações, componentes e possíveis problemas.
    """
    typer.echo("🔍 Executando verificação de saúde do Nível 2...")
    
    issues_found = []
    warnings_found = []
    
    # Verificar diretório base
    setores_path = Path("src/config/setores")
    if not setores_path.exists():
        issues_found.append("Diretório src/config/setores não encontrado")
        typer.echo("❌ Diretório base não encontrado")
        raise typer.Exit(1)
    
    # Verificar template
    template_path = setores_path / "nivel2_template"
    if not template_path.exists():
        warnings_found.append("Template nivel2_template não encontrado")
    else:
        required_template_files = ["domain_config.json", "patterns.json", "column_aliases.json"]
        for file_name in required_template_files:
            if not (template_path / file_name).exists():
                warnings_found.append(f"Arquivo de template ausente: {file_name}")
    
    # Encontrar e verificar configurações
    config_count = 0
    valid_configs = 0
    
    for setor_dir in setores_path.iterdir():
        if setor_dir.is_dir() and setor_dir.name != "nivel2_template":
            for client_dir in setor_dir.iterdir():
                if client_dir.is_dir():
                    nivel2_dir = client_dir / "nivel2"
                    if nivel2_dir.exists():
                        config_count += 1
                        
                        # Verificação rápida de validação
                        try:
                            from scripts.validate_nivel2 import validate_nivel2_setup
                            result = validate_nivel2_setup(setor_dir.name, client_dir.name)
                            
                            if result['valid']:
                                valid_configs += 1
                            else:
                                issues_found.append(f"Configuração inválida: {setor_dir.name}/{client_dir.name}")
                        except Exception as e:
                            issues_found.append(f"Erro ao validar {setor_dir.name}/{client_dir.name}: {e}")
    
    # Verificar imports necessários
    try:
        from src.tools.nivel2_factory import Nivel2Factory
        typer.echo("✅ Nivel2Factory importado com sucesso")
    except ImportError as e:
        issues_found.append(f"Erro ao importar Nivel2Factory: {e}")
    
    # Resultados
    typer.echo(f"\n📊 Resumo da Verificação:")
    typer.echo(f"   Configurações encontradas: {config_count}")
    typer.echo(f"   Configurações válidas: {valid_configs}")
    
    if issues_found:
        typer.echo(f"\n❌ Problemas encontrados ({len(issues_found)}):")
        for issue in issues_found:
            typer.echo(f"   • {issue}")
    
    if warnings_found:
        typer.echo(f"\n⚠️  Avisos ({len(warnings_found)}):")
        for warning in warnings_found:
            typer.echo(f"   • {warning}")
    
    if not issues_found and not warnings_found:
        typer.echo("\n✅ Sistema Nível 2 está saudável!")
        typer.echo("   Todas as verificações passaram com sucesso")
    elif not issues_found:
        typer.echo("\n✅ Sistema Nível 2 está funcionando")
        typer.echo("   Apenas avisos encontrados, nenhum problema crítico")
    else:
        typer.echo("\n❌ Problemas críticos encontrados no sistema Nível 2")
        raise typer.Exit(1)

@app.command()
def benchmark(
    sector: str = typer.Argument(..., help="Nome do setor"),
    client: str = typer.Argument(..., help="ID do cliente"),
    question: str = typer.Option("Quanto vendemos em euro e USD em 2023?", "--question", "-q", help="Pergunta para teste")
):
    """
    Executa benchmark de performance do Nível 2 para um cliente.
    Testa pattern matching, schema compaction e outros componentes.
    """
    typer.echo(f"🚀 Executando benchmark para {sector}/{client}")
    
    from src.tools.nivel2_factory import Nivel2Factory
    
    # Verificar se está habilitado
    if not Nivel2Factory.is_nivel2_enabled(sector, client):
        typer.echo("❌ Nível 2 não habilitado para este cliente")
        raise typer.Exit(1)
    
    typer.echo(f"📝 Pergunta de teste: {question}")
    
    # Benchmark de Pattern Matching
    typer.echo("\n🔍 Testando Pattern Matching...")
    try:
        matcher = Nivel2Factory.create_pattern_matcher(sector, client)
        if matcher:
            start_time = datetime.now()
            pattern_name, pattern_config = matcher.match_pattern(question)
            end_time = datetime.now()
            
            pattern_time = (end_time - start_time).total_seconds() * 1000
            
            if pattern_config:
                typer.echo(f"✅ Pattern detectado: '{pattern_name}' ({pattern_time:.2f}ms)")
                
                # Testar geração de SQL
                start_time = datetime.now()
                sql = matcher.generate_sql_from_pattern(question, pattern_config)
                end_time = datetime.now()
                
                sql_gen_time = (end_time - start_time).total_seconds() * 1000
                
                if sql:
                    typer.echo(f"✅ SQL gerado: {len(sql)} caracteres ({sql_gen_time:.2f}ms)")
                else:
                    typer.echo("❌ Falha na geração de SQL")
            else:
                typer.echo(f"❌ Nenhum pattern detectado ({pattern_time:.2f}ms)")
        else:
            typer.echo("❌ Pattern matcher não disponível")
    except Exception as e:
        typer.echo(f"❌ Erro no pattern matching: {e}")
    
    # Benchmark de Schema Compaction
    typer.echo("\n📊 Testando Schema Compaction...")
    try:
        compactor = Nivel2Factory.create_schema_compactor(sector, client)
        if compactor:
            # Usar schema exemplo
            schema_path = "src/config/setores/cambio/L2M/L2M_schema.json"
            if Path(schema_path).exists():
                with open(schema_path, 'r') as f:
                    schema = json.load(f)
                
                start_time = datetime.now()
                compacted = compactor.compact(schema, question)
                end_time = datetime.now()
                
                compact_time = (end_time - start_time).total_seconds() * 1000
                
                original_size = len(json.dumps(schema))
                compacted_size = len(compacted)
                reduction = (1 - compacted_size / original_size) * 100
                
                typer.echo(f"✅ Schema compactado: {reduction:.1f}% redução ({compact_time:.2f}ms)")
                typer.echo(f"   Original: {original_size} chars → Compactado: {compacted_size} chars")
            else:
                typer.echo("⚠️  Schema exemplo não encontrado para teste")
        else:
            typer.echo("❌ Schema compactor não disponível")
    except Exception as e:
        typer.echo(f"❌ Erro na compactação: {e}")
    
    # Benchmark de Domain Extraction
    typer.echo("\n🎯 Testando Domain Extraction...")
    try:
        extractor = Nivel2Factory.create_domain_extractor(sector, client)
        if extractor:
            test_extractions = [
                ("currency", "euro"),
                ("currency", "USD"),
                ("year", "2023"),
                ("operation_type", "vendemos")
            ]
            
            total_time = 0
            successful_extractions = 0
            
            for extractor_name, test_text in test_extractions:
                start_time = datetime.now()
                result = extractor.extract(extractor_name, test_text)
                end_time = datetime.now()
                
                extraction_time = (end_time - start_time).total_seconds() * 1000
                total_time += extraction_time
                
                if result:
                    typer.echo(f"✅ {extractor_name}('{test_text}') → '{result}' ({extraction_time:.2f}ms)")
                    successful_extractions += 1
                else:
                    typer.echo(f"❌ {extractor_name}('{test_text}') → None ({extraction_time:.2f}ms)")
            
            typer.echo(f"📊 Total: {successful_extractions}/{len(test_extractions)} sucessos, {total_time:.2f}ms total")
        else:
            typer.echo("❌ Domain extractor não disponível")
    except Exception as e:
        typer.echo(f"❌ Erro na extração: {e}")
    
    typer.echo("\n✅ Benchmark concluído!")

if __name__ == "__main__":
    # Verificar se estamos no diretório correto
    if not Path("src/config/setores").exists():
        typer.echo("❌ Erro: Execute este script a partir do diretório raiz do projeto")
        typer.echo("   (onde existe src/config/setores/)")
        raise typer.Exit(1)
    
    app()