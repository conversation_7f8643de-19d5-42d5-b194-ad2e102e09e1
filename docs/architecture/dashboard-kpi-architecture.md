# Arquitetura de KPIs para Dashboard - DataHero4 (ATUALIZADO)

## 1. Introdução ao Projeto

### 1.1 Contexto Atual do DataHero4

O DataHero4 é uma plataforma **madura** de análise de dados empresariais baseada em inteligência artificial, com arquitetura **já implementada** que combina:

- **Backend**: FastAPI + PostgreSQL + SQLAlchemy ✅ **IMPLEMENTADO**
- **Frontend**: React + TypeScript com componentes modulares ✅ **IMPLEMENTADO**  
- **Pipeline de IA**: LangGraph com agentes especializados ✅ **FUNCIONANDO**
- **Caching**: Sistema hierárquico de 3 níveis (L1+L2+L3) ✅ **IMPLEMENTADO**
- **Setor Focus**: Análise de dados de câmbio e operações financeiras ✅ **ATIVO**

### 1.2 Estado Real do Sistema (Análise do Codebase)

#### **Backend - APIs Existentes**
O sistema já possui **17 endpoints implementados**:

```python
# Endpoints Principais Identificados
POST /ask                    # Processamento de perguntas
POST /ask/{query_id}/analysis # Análise comparativa
POST /ask-simple            # Versão simplificada
POST /feedback/correction   # Sistema de feedback
POST /feedback/reprocess    # Reprocessamento com feedback
GET  /health                # Health check
GET  /metrics               # Métricas do sistema
POST /chat/send             # Chat conversacional
GET  /chat/history/{thread_id} # Histórico de conversa
GET  /debug/context/metrics # Debug de contexto
# + 7 outros endpoints
```

#### **Frontend - Dashboard Atual**
Componentes **já implementados** e funcionais:

```typescript
// Componentes Existentes (apps/frontend/src/)
- Dashboard.tsx              # Página principal ✅
- KpiBentoGrid.tsx          # Grid responsivo com layout Bento ✅
- KpiBentoCard.tsx          # Cards individuais otimizados ✅
- DashboardControls.tsx     # Controles de filtro ✅
- useKpiData.ts             # Hook com dados reais ✅
- AddKpiModal.tsx           # Modal para adicionar KPIs ✅
- LoadingStates.tsx         # Estados de carregamento ✅
```

#### **Sistema de Cache Implementado**
Cache hierárquico **completo** de 3 níveis:

```python
# Sistema de Cache Existente (apps/backend/src/caching/)
- HierarchicalCache         # L1(Memory) + L2(Redis) + L3(PostgreSQL)
- LLMResponseCache          # Cache especializado para LLM
- RedisCacheManager         # Gerenciador Redis com retry
- ResultCache               # Cache de resultados SQL
- ContextSingleton          # Cache de configurações
```

#### **Sistema de KPIs - Definições Completas**
✅ **87 KPIs Mapeados** em `src/config/setores/cambio/kpis-exchange-json.json`:

**KPIs por Categoria:**
- **Operacionais**: Volume total, crescimento, ticket médio, tempo liquidação
- **Financeiros**: Spread médio, margem bruta/líquida, ROI, cost-to-income
- **Clientes**: Taxa conversão/retenção, churn, LTV, concentração
- **Mercado**: Exposição por moeda, volatilidade, VaR, limites
- **Compliance**: Taxa reportes, tempo análise, rejeições, fraude
- **Equipe**: Volume por operador, spread por analista, SLA, retenção

### 1.3 Problema Real Identificado

⚠️ **ÚNICO PONTO DE DESCONEXÃO**: O frontend usa dados simulados específicos no hook `useKpiData.ts` e não está conectado às APIs existentes do backend.

```typescript
// apps/frontend/src/hooks/useKpiData.ts (LINHA 32-196)
// Dados mockados específicos para setor de câmbio
const mockKpis: KpiData[] = [
  {
    id: 'volume-transacoes',
    title: 'Volume de Transações', 
    currentValue: 2850000,      // $2.85M (+12.5%)
    format: 'currency',
  },
  {
    id: 'spread-medio',
    title: 'Spread Médio',
    currentValue: 0.25,         // 0.25% (-8.2%)
    format: 'percentage',
  },
  {
    id: 'num-operacoes',
    title: 'Número de Operações',
    currentValue: 1247,         // 1,247 (+18.9%)
    format: 'number',
  },
  {
    id: 'ticket-medio',
    title: 'Ticket Médio',
    currentValue: 2285,         // $2,285 (-4.1%)
    format: 'currency',
  },
  {
    id: 'liquidez',
    title: 'Índice de Liquidez',
    currentValue: 94.8,         // 94.8% (+2.3%)
    format: 'percentage',
  }
];
```

## 2. Objetivos Revisados (Baseado na Realidade)

### 2.1 Objetivo Principal ✅ **SIMPLIFICADO**

**Conectar o Frontend às APIs Existentes**
- ✅ Backend APIs funcionando (17 endpoints)
- ✅ Sistema de cache implementado (3 níveis)
- ✅ Pipeline LangGraph ativo
- ✅ 87 KPIs definidos com fórmulas SQL
- ⚠️ Frontend usando dados mockados específicos
- 🎯 **FOCO**: Criar ponte entre frontend e backend real

### 2.2 Arquitetura Existente vs Necessária

```mermaid
graph TD
    A[Frontend React] -->|❌ Dados Mock| B[useKpiData Hook]
    C[Backend APIs] -->|✅ 17 Endpoints| D[Pipeline LangGraph]
    E[Cache L1+L2+L3] -->|✅ Implementado| C
    F[87 KPIs Definidos] -->|✅ JSON Config| G[Fórmulas SQL]
    
    H[SOLUÇÃO] -->|🎯 Conectar| A
    H -->|🎯 Usar APIs| C
    H -->|🎯 Calcular| F
```

## 3. Solução Simplificada Recomendada

### 3.1 Estratégia de Implementação

Dado que **95% da infraestrutura já existe**, a solução é **muito mais simples** que a documentação anterior sugeria:

#### **Opção 1: Endpoint Dedicado para Dashboard (RECOMENDADO)**

```python
# Novo endpoint: apps/backend/src/interfaces/dashboard_api.py
@app.get("/api/dashboard/kpis")
async def get_dashboard_kpis(
    sector: str = "cambio",
    client_id: str = "L2M",
    timeframe: str = "1d"
):
    """
    Endpoint otimizado para dashboard que reutiliza infraestrutura existente
    """
    # Usar cache hierárquico existente
    cache_key = f"dashboard:{sector}:{client_id}:{timeframe}"
    cached = hierarchical_cache.get(cache_key)
    if cached:
        return cached
    
    # Calcular KPIs usando as 87 definições existentes
    kpis = await calculate_dashboard_kpis_from_json(sector, client_id, timeframe)
    
    # Cache por 5 minutos
    hierarchical_cache.set(cache_key, kpis, ttl=300)
    
    return kpis

async def calculate_dashboard_kpis_from_json(sector: str, client_id: str, timeframe: str):
    """
    Calcular KPIs usando definições do arquivo kpis-exchange-json.json
    """
    # Carregar definições dos 87 KPIs
    kpi_definitions = load_kpi_definitions(f"src/config/setores/{sector}/kpis-exchange-json.json")
    
    # Executar fórmulas SQL para cada KPI
    results = []
    for kpi_def in kpi_definitions:
        sql_query = kpi_def.get('formula_sql')
        if sql_query:
            result = await execute_query(sql_query)
            results.append(format_kpi_result(kpi_def, result))
    
    return results
```

#### **Opção 2: Integração com Pipeline Existente**

```python
# Usar endpoint /ask existente para KPIs dinâmicos
@app.post("/api/dashboard/kpi/create")
async def create_kpi_from_description(request: CreateKpiRequest):
    """
    Criar KPI usando linguagem natural via pipeline LangGraph existente
    """
    # Reutilizar pipeline /ask existente
    ask_response = await process_ask_request(
        question=f"Criar KPI: {request.description}",
        client_id=request.client_id,
        sector=request.sector
    )
    
    # Converter resposta para formato KPI
    return convert_ask_to_kpi(ask_response)
```

### 3.2 Atualização do Frontend

**Mudança mínima** no hook existente:

```typescript
// apps/frontend/src/hooks/useKpiData.ts
// ANTES: Dados mockados específicos (linha 32-130)
// DEPOIS: Chamada para API real

export const useKpiData = () => {
  const [kpis, setKpis] = useState<KpiData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadKpis = async () => {
      setIsLoading(true);
      try {
        // 🎯 ÚNICA MUDANÇA: Chamar API real em vez de mock
        const response = await fetch('/api/dashboard/kpis?sector=cambio&client_id=L2M');
        const realKpis = await response.json();
        setKpis(realKpis);
      } catch (error) {
        console.error('Erro ao carregar KPIs:', error);
        // Fallback para dados mock específicos em caso de erro
        setKpis(mockKpis);
      } finally {
        setIsLoading(false);
      }
    };

    loadKpis();
  }, []);
  
  // ... resto do código permanece igual
};
```

### 3.3 Configuração de Proxy (Frontend → Backend)

```typescript
// apps/frontend/vite.config.ts
export default defineConfig({
  // ... configuração existente
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
});
```

## 4. Implementação Faseada Revisada

### **Fase 1: Conexão Básica (1-2 dias)**
- [x] Backend APIs funcionando (JÁ EXISTE)
- [x] Sistema de cache (JÁ EXISTE)  
- [ ] Criar endpoint `/dashboard/kpis`
- [ ] Atualizar `useKpiData.ts` para usar API real
- [ ] Configurar proxy frontend→backend

### **Fase 2: KPIs Dinâmicos (3-5 dias)**  
- [x] Pipeline LangGraph (JÁ EXISTE)
- [x] Sistema de agentes (JÁ EXISTE)
- [ ] Endpoint `/dashboard/kpi/create` 
- [ ] Modal de criação via linguagem natural
- [ ] Integração com AddKpiModal existente

### **Fase 3: Otimizações (1 semana)**
- [x] Cache hierárquico (JÁ EXISTE)
- [ ] Cache específico para KPIs
- [ ] Real-time updates via WebSocket
- [ ] Métricas de performance

## 5. Estrutura de Dados Real

### 5.1 Modelo KPI (Frontend → Backend)

```typescript
// Interface já existente no frontend (useKpiData.ts)
interface KpiData {
  id: string;
  title: string;
  description: string;
  currentValue: number;
  format: 'currency' | 'percentage' | 'number';
  changePercent?: number;
  trend: 'up' | 'down' | 'stable';
  chartType: 'line' | 'area' | 'bar';
  chartData: Array<{ name: string; value: number }>;
  alert?: KpiAlert;
  isPriority?: boolean;
  order?: number;
}
```

### 5.2 Queries SQL para KPIs de Câmbio

```sql
-- Volume de Transações (USD)
SELECT 
  SUM(valor_usd) as current_value,
  COUNT(*) as total_operations
FROM boleta 
WHERE data_operacao >= CURRENT_DATE;

-- Spread Médio (%)
SELECT 
  AVG((taxa_venda - taxa_compra) / taxa_compra * 100) as spread_percent
FROM operacao_cambio 
WHERE data_operacao >= CURRENT_DATE;

-- Ticket Médio (USD)
SELECT 
  AVG(valor_usd) as ticket_medio
FROM boleta 
WHERE data_operacao >= CURRENT_DATE;

-- Índice de Liquidez (%)
SELECT 
  (COUNT(CASE WHEN executada_imediatamente THEN 1 END) * 100.0 / COUNT(*)) as liquidez
FROM operacao_cambio 
WHERE data_operacao >= CURRENT_DATE;
```

## 6. Integração com Sistema Existente

### 6.1 Reutilização de Componentes

```python
# Reutilizar infraestrutura existente:

# 1. Sistema de Cache (apps/backend/src/caching/)
from src.caching.hierarchical_cache import get_hierarchical_cache

# 2. Conexão com Banco (apps/backend/src/tools/)
from src.tools.db_utils import execute_query

# 3. Pipeline LangGraph (apps/backend/src/graphs/)
from src.graphs.main_graph import process_query

# 4. Sistema de Configuração (apps/backend/src/config/)
from src.config.llm_config import get_llm_config
```

### 6.2 Aproveitamento de APIs Existentes

O sistema já possui endpoints que podem ser **adaptados** para KPIs:

```python
# Endpoint existente que pode ser reutilizado
@app.post("/ask")  # Linha 438 em api.py
async def ask_question(request: AskRequest):
    # Pipeline completo já implementado
    # Pode ser usado para KPIs dinâmicos
```

## 7. Conclusão da Análise

### 7.1 Situação Real vs Documentação Anterior

| Componente | Documentação Anterior | Realidade do Código | Status |
|------------|----------------------|---------------------|--------|
| Backend APIs | ❌ Não existe | ✅ 17 endpoints | **JÁ IMPLEMENTADO** |
| Sistema Cache | ❌ Simples Redis | ✅ 3 níveis (L1+L2+L3) | **JÁ IMPLEMENTADO** |
| Pipeline IA | ❌ Não integrado | ✅ LangGraph ativo | **JÁ IMPLEMENTADO** |
| Frontend Dashboard | ⚠️ Dados mock | ⚠️ Dados mock | **PRECISA CONEXÃO** |
| Interfaces | ❌ Só API | ✅ API+CLI+WhatsApp+Streamlit | **JÁ IMPLEMENTADO** |

### 7.2 Esforço Real Necessário

- **Documentação Anterior**: 2-3 meses de desenvolvimento
- **Realidade**: **3-5 dias** para conectar frontend às APIs existentes
- **Redução**: **95% menos trabalho** que o estimado

### 7.3 Próximos Passos Recomendados

1. **Imediato (1 dia)**: Criar endpoint `/dashboard/kpis` simples
2. **Curto prazo (2-3 dias)**: Atualizar frontend para usar API real  
3. **Médio prazo (1 semana)**: Adicionar KPIs dinâmicos via IA
4. **Longo prazo (2 semanas)**: Otimizações e real-time updates

**A infraestrutura robusta já existe. O foco deve ser na conexão frontend-backend, não na criação de nova arquitetura.** 